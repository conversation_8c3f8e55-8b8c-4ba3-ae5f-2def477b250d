{% extends 'base.html.twig' %}

{% block body %}
    {% if app.request.query.get('idProject') is defined and app.request.query.get('idProject') != null %}
        <div class="col-12">
            <a class="mt-2" href="{{ path('project_show' , {id: app.request.query.get('idProject')}) }}"><em
                        class="icon icon-arrow-left-circle"></em></a>
        </div>
    {% endif %}
    <div class="col-12">
        <h1>
            {% block title %}Recherche{% endblock %}
        </h1>
    </div>
    {% set sum = 0 %}
    {% for task in tasks %}
        {% if task.resume.duration is defined %}
            {% set sum = sum + task.resume.duration %}
        {% else %}
            {% set sum = sum + task.duration %}
        {% endif %}
    {% endfor %}
    <div class="col-12">
        <div class="form-search">
            {{ include("recherche/_form.html.twig") }}
        </div>
    </div>
    <div class="col-12">
        <div class="card-deck">
            <div class="card bg-light mb-3  text-center font-weight-bold">
                <div class="card-header">Période</div>
                <div class="card-body">
                    <h5 class="card-title font-weight-bold">{{ dates[0]|date('d/m/Y')|default('~') }} -
                        {{ dates[1]|date('d/m/Y')|default('~') }}
                    </h5>
                </div>
            </div>
            <div class="card bg-light mb-3  text-center font-weight-bold">
                <div class="card-header">Nombre d'entrées</div>
                <div class="card-body">
                    <h5 class="card-title font-weight-bold">{{ tasks | length }}</h5>
                </div>
            </div>
            <div class="card bg-light mb-3  text-center font-weight-bold">
                <div class="card-header">Charge consommée (nb de jour)</div>
                <div class="card-body">
                    <h5 class="card-title font-weight-bold">{{ (sum / 7.5)|number_format(2, '.', ' ') }}</h5>
                </div>
            </div>
        </div>
    </div>
    {% if app.request.query.get('idProject') is defined and app.request.query.get('idProject') != null %}
        <form method="post" id="form-checkbox" action="{{ path('change_project', {id: app.request.query.get('idProject')}) }}">
    {% endif %}
    <div id="results_filters">
        <div class="{{ _col_table }}">

            {% if cumulativeSearch is defined and cumulativeSearch %}

                <table class="table">
                    <thead>
                        <tr>
                            <th>{{ ('search.table.'~ cumulativeSearch) | trans() }}</th>
                            <th>Période</th>
                            <th>Durée</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% set rowID = 0 %}
                    {% for id,task in tasks%}
                        <tr class="main-result" data-toggle="collapse" href="#task-{{ rowID }}">
                            <td>{{ id }}</td>
                            <td>
                                {% if task.resume.date_from is not same as task.resume.date_to %}
                                    {{ task.resume.date_from|date('d/m/Y') }} au {{ task.resume.date_to|date('d/m/Y') }}
                                {% else %}
                                    {{ task.resume.date_from|date('d/m/Y') }}
                                {% endif %}
                            </td>
                            <td>{{ (task.resume.duration/7)|number_format(2, '.', ' ') }}</td>
                        </tr>
                        <tr id="task-{{ rowID }}"  class="details collapse">
                            <td class="sub-table" colspan="999">
                                {% include 'recherche/table.html.twig' with {'tasks': task.details} %}
                            </td>
                        </tr>
                        {% set rowID = rowID + 1 %}
                    {% endfor %}
                    </tbody>
                </table>

{#                {% include 'recherche/table.html.twig' %}#}
            {% else %}
                {% include 'recherche/table.html.twig' %}
            {% endif %}

        </div>
        <div id="tbody"></div>
    </div>
    {% if app.request.query.get('idProject') is defined and app.request.query.get('idProject') != null %}
        <div id="affectation-task" style="display: none" class="col-12">
            {{ form_row(projectForm, {'label':false}) }}
            <br/><br/>
            <input class="btn btn-outline-primary mb-2" type="submit" value="Modifier l'affectation"/>
        </div>
        </form>
        <div class="col-12">
            <a href="{{ path('project_show' , {id: app.request.query.get('idProject')}) }}"
               style="padding-top: 7px;"><em class="icon icon-arrow-left-circle"></em></a>
        </div>
    {% endif %}
    <script>
        function redirectToCurrentRouteWithParam() {
            const url = new URL(window.location.href);
            url.searchParams.set('showCumulativeDuration', 'true');
            window.location.href = url.toString();
        }
    </script>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('search_filters') }}
{% endblock %}
