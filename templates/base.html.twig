{% import "_tpl/macro.html.twig" as mac %}

{% set _col_form = 'col-11 col-md-10 col-lg-8 col-content mx-auto max-w-md' %}
{% set _col_table = 'col col-content' %}

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <title>
        {% block title %}Welcome!{% endblock %}
    </title>
    {% block stylesheets %}
        {{ encore_entry_link_tags("app") }}
    {% endblock %}
</head>

<body id="{% block id_page %}{% endblock %}" class="bg-light {% block page_class %}{% endblock %}">
<header class="blog-header py-2 container-fluid">
    <div class="row flex-nowrap justify-content-between align-items-center">
        <div class="col" id="logo">
            <img class="logo navbar-brand mt-2" src="{{ asset('build/logo_sdt.png') }}"/>
        </div>
        <div class="col-4 text-center d-none d-md-block">
            <span class="header-title">
              {#Extranet ITROOM#}</span></div>
        <div class="col-4 d-flex justify-content-end">
            <div class="d-flex flex-column">
                {% if app.user %}
                    <a class="my-2 text-info" href="{{ path('user_show', { id: app.user.id }) }}">
                        {{ app.user.firstname }}
                    </a>
                    <a class="text-secondary" href="{{ path('security_logout') }}">Déconnexion</a>
                {% endif %}
            </div>
        </div>
    </div>

</header>
{% block navbar %}
    <nav class="navbar navbar-expand-lg navbar-light p-2 border-top border-bottom">
        {#<a class="navbar-brand" href="#">Navbar</a>#}
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarColor01">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-center" id="navbarColor01">
            <ul class="navbar-nav mr-4">
                {# macro qui retourne un item de menu, les paramètres sont (libellé,
                      chemin) #}
                {{ mac.menuItem("Accueil", path("home")) }}
                {% if app.user %}
                    {{ mac.menuItem("Saisir une intervention", path("task_new")) }}
                    {% set subs = [
                        {"url": path("conge_index"), "name": "Mes congés"},
                        {"url": path("conge_dashboard"), "name": "Tableau de bord"},
                        {"url": path("conge_calendrier"), "name": "Calendrier"}
                    ] %}
                    {% if is_granted('ROLE_GESTION_CONGES') %}
                        {% set subs = subs|merge([{"url": path("conge_validation"), "name": "Validation"}]) %}
                    {% endif %}
                    {{ mac.menuItem("Congés", path("conge_index"), subs) }}
                    {% set subsinterventions = [
                        {"url": path("calendar"), "name": "Calendrier"},
                        {"url": path("type_inter_actif_index"), "name": "Type d'intervention"},
                        {"url": path("reporting_time_check"), "name": "Vérification des temps"}
                    ] %}
                    {% if is_granted('ROLE_ADMIN') %}
                        {% set subsinterventions = subsinterventions|merge([{"url": path("weekly_report_selector"), "name": "Rapport hebdomadaire"}]) %}
                    {% endif %}
                    {{ mac.menuItem("Interventions", path("task_index"), subsinterventions) }}
                    {{ mac.menuItem("Recherche", path("recherche")) }}
                    {% set subsclients = [
                        {"url": path("client_index"), "name": "Clients"},
                        {"url": path("client_actif_index"), "name": "Clients actifs"}
                    ] %}
                    {{ mac.menuItem("Client", path("client_index"), subsclients) }}
                    {{ mac.menuItem("Utilisateur", path("user_index")) }}
                    {{ mac.menuItem("Projet", path("project_index" , {state: 1 ,type: 1} )) }}
                    {% if is_granted('ROLE_ADMIN') and app.user.company.name|default('') == "IT-Room" %}
                        {{ mac.menuItem("Suivi TMA", path("suivi_tma")) }}
                    {% endif %}
                    {{ mac.menuItem("Entité", path("company_index")) }}
                    {% if not app.user %}
                        {{ mac.menuItem("Connexion", path("security_login")) }}
                    {% endif %}
                    {% if app.user.company.name|default('') == "IT-Room" %}
                        {{ mac.menuItem("Reporting", path("reporting_index")) }}
                    {% endif %}
                {% endif %}
            </ul>
        </div>
    </nav>
{% endblock %}
<div class="container">
    <div class="row justify-content-md-center mt-3">
        <div class="col-12">
            {% for label, messages in app.flashes %}
                {% for message in messages %}
                    <div class="alert alert-primary">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endfor %}
        </div>
        {% block body %}{% endblock %}
    </div>
</div>

{% block flashes %}
    <div class="container">
        {% for label, messages in app.flashes %}
            {% for message in messages %}
                <div class="alert alert-primary">
                    {{ message }}
                </div>
            {% endfor %}
        {% endfor %}
    </div>
{% endblock %}

{% block javascripts %}
    {{ encore_entry_script_tags("app") }}
{% endblock %}
{{ include("_tpl/icons.html.twig") }}
</body>
</html>
