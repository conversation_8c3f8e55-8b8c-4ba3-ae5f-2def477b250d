{% extends 'base.html.twig' %}

{% block title %}<PERSON><PERSON>er un congé pour un utilisateur{% endblock %}

{% block body %}
<div class="col-12">
    <h1>{% block page_title %}<PERSON>réer un congé pour un utilisateur{% endblock %}</h1>
    <p class="text-muted">En tant qu'administrateur, vous pouvez créer un congé ou un arrêt maladie pour n'importe quel utilisateur.</p>
</div>

<div class="col-12">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-user-plus"></i> Nouveau congé/arrêt maladie
            </h5>
        </div>
        <div class="card-body">
            {{ form_start(form) }}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form_label(form.user) }}
                        {{ form_widget(form.user, {'attr': {'class': 'form-control'}}) }}
                        {{ form_errors(form.user) }}
                        <small class="form-text text-muted">Sélectionnez l'utilisateur pour lequel créer le congé</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form_label(form.reason) }}
                        {{ form_widget(form.reason, {'attr': {'class': 'form-control'}}) }}
                        {{ form_errors(form.reason) }}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form_label(form.dateDebut) }}
                        {{ form_widget(form.dateDebut, {'attr': {'class': 'form-control'}}) }}
                        {{ form_errors(form.dateDebut) }}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form_label(form.dureeDebut) }}
                        {{ form_widget(form.dureeDebut, {'attr': {'class': 'form-control'}}) }}
                        {{ form_errors(form.dureeDebut) }}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form_label(form.dateFin) }}
                        {{ form_widget(form.dateFin, {'attr': {'class': 'form-control'}}) }}
                        {{ form_errors(form.dateFin) }}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form_label(form.dureeFin) }}
                        {{ form_widget(form.dureeFin, {'attr': {'class': 'form-control'}}) }}
                        {{ form_errors(form.dureeFin) }}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Créer le congé
                    </button>
                    <a href="{{ path('conge_index') }}" class="btn btn-secondary ml-2">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>

            {{ form_end(form) }}
        </div>
    </div>
</div>

<div class="col-12 mt-4">
    <div class="alert alert-info">
        <h6><i class="fas fa-info-circle"></i> Informations importantes :</h6>
        <ul class="mb-0">
            <li>Le congé sera créé avec le statut "En Attente" et devra être validé par un gestionnaire de congés</li>
            <li>L'utilisateur concerné recevra une notification par email</li>
            <li>Les week-ends et jours fériés ne peuvent pas être sélectionnés</li>
            <li>Vous pouvez créer des congés ou des arrêts maladie selon le motif sélectionné</li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Même logique que pour le formulaire normal de congé
    let dateDebut = document.getElementById('admin_conge_dateDebut');
    let dateFin = document.getElementById('admin_conge_dateFin');
    let typeJourneeFin = document.getElementById('admin_conge_dureeFin');
    let typeJourneeDebut = document.getElementById('admin_conge_dureeDebut');
    
    if (dateDebut && dateFin && typeJourneeFin && typeJourneeDebut) {
        function updateDureeFields() {
            let parentFin = typeJourneeFin.closest('.form-group');
            let labelDebut = typeJourneeDebut.closest('.form-group').querySelector('label');
            
            if (parentFin && labelDebut) {
                parentFin.style.display = 'block';
                labelDebut.textContent = 'Durée début';
                
                if (dateFin.value === dateDebut.value) {
                    parentFin.style.display = 'none';
                    labelDebut.textContent = 'Durée';
                }
            }
        }
        
        dateDebut.addEventListener('change', updateDureeFields);
        dateFin.addEventListener('change', updateDureeFields);
        updateDureeFields(); // Appel initial
    }
});
</script>
{% endblock %}
