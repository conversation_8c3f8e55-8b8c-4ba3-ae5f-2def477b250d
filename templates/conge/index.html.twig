{% extends 'base.html.twig' %}
{% import "_tpl/macro.html.twig" as macro %}
{% block body %}
    {% set is_admin = is_granted('ROLE_GESTION_CONGES') %}
    <div class="col-12">
        <h1>{% block title %}Congé{% endblock %}</h1>
    </div>
    <div class="col-12 mb-3 conge-add-container">
        <button class="btn btn-outline-primary" data-toggle="modal" data-target="#modal-conge-new" >Ajouter un congé
        </button>
        {% if is_granted('ROLE_ADMIN') %}
            <a href="{{ path('conge_admin_new') }}" class="btn btn-outline-success ml-2">
                <i class="fas fa-user-plus"></i> Créer un congé pour un utilisateur
            </a>
        {% endif %}
        <em class="icon-info icon icon-action" data-toggle="modal" data-target="#modal-conge-info"
            title="Comment est calculé mon solde de congés ?"></em>
    </div>
    <div class="col-12">
        <table class="table">
            <thead>
            <tr>
                <th>Date de début</th>
                <th>Date de fin</th>
                <th>Date de création</th>
                <th>Etat</th>
                <th>Motif</th>
                <th>Action</th>
            </tr>
            </thead>
            <tbody>
            {% for conge in conges %}
                <tr>
                    <td>
                        {{ macro.format_date_conge(conge.dateDebut, conge.dureeDebut) }}
                    </td>
                    <td>
                        {{ macro.format_date_conge(conge.dateFin, conge.dureeFin) }}
                    </td>
                    <td>
                        {{ conge.dateCreation|date('d/m/y') }}
                    </td>
                    <td class="text-capitalize conge-etat-{{ conge.etat|replace({" ": "-", "é": "e"})|lower }}">
                        {{ conge.etat }}
                    </td>
                    <td>
                        {{ conge.reason|trans() }}
                    </td>
                    <td>
                        <a class="btn btn-sm btn btn-danger" href="{{ path('conge_delete', { 'id': conge.id }) }}">
                            Supprimer</a>
                        <a class="btn btn-sm btn-primary" href="{{ path('conge_edit', { 'id': conge.id }) }}">
                            Modifier
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="modal " tabindex="-1" role="dialog" id="modal-conge-new" data-auto-show="{{ open_form is defined ? open_form }}">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                {{ form_start(form) }}
                <div class="modal-header">
                    <h5 class="modal-title">Création d'une demande de congé</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    {{ macro.form_2column(form) }}
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Confirmer</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                </div>
                {{ form_end(form) }}
            </div>
        </div>
    </div>

    <div class="modal" tabindex="-1" role="dialog" id="modal-conge-info">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Comment est calculé mon solde de congés ?</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>
                        Selon la convention, tu cotises 2,08 jours de congés par mois, ce qui correspond à 25 jours
                        ouvrès (30 jours ouvrables).
                        Les droits à congès s'acquièrent du 1er juin au 31 mai de l'année suivante.
                        <br><br>
                        Par ailleurs, 7 jours de RTT s'acquièrent du 01/01 au 31/12 à raison de 0,58 jour par mois. Tu
                        peux poser une journée dès que le nombre de jours
                        acquis est >= 1. Les RTT sont à poser avant le 31 Décembre en considération des besoins de
                        l'entreprise.
                    </p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags("conge") }}
{% endblock %}

