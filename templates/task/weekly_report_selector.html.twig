{% extends 'base.html.twig' %}

{% block title %}Sélection du rapport hebdomadaire{% endblock %}

{% block body %}
<div class="col-12">
    <h1>{% block page_title %}Rapport hebdomadaire{% endblock %}</h1>
    <p class="text-muted">Sélectionnez une semaine ou une plage de semaines pour générer le rapport.</p>
</div>

<div class="col-12">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Sélection des semaines</h5>
        </div>
        <div class="card-body">
            <form id="weekSelectorForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="weekType">Type de sélection :</label>
                            <select class="form-control" id="weekType" name="weekType">
                                <option value="single">Se<PERSON><PERSON> unique</option>
                                <option value="range">Plage de semaines</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div id="singleWeekSection">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="singleWeek">Numéro de semaine :</label>
                                <input type="number" class="form-control" id="singleWeek" name="singleWeek" 
                                       min="1" max="53" value="{{ currentWeek }}" placeholder="Ex: 25">
                                <small class="form-text text-muted">Semaine de 1 à 53</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="rangeWeekSection" style="display: none;">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="startWeek">Semaine de début :</label>
                                <input type="number" class="form-control" id="startWeek" name="startWeek" 
                                       min="1" max="53" value="{{ currentWeek }}" placeholder="Ex: 24">
                                <small class="form-text text-muted">Semaine de 1 à 53</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="endWeek">Semaine de fin :</label>
                                <input type="number" class="form-control" id="endWeek" name="endWeek" 
                                       min="1" max="53" value="{{ currentWeek }}" placeholder="Ex: 26">
                                <small class="form-text text-muted">Semaine de 1 à 53</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-chart-line"></i> Générer le rapport
                        </button>
                        <a href="{{ path('task_index') }}" class="btn btn-secondary ml-2">
                            <i class="fas fa-arrow-left"></i> Retour aux interventions
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const weekTypeSelect = document.getElementById('weekType');
    const singleWeekSection = document.getElementById('singleWeekSection');
    const rangeWeekSection = document.getElementById('rangeWeekSection');
    const form = document.getElementById('weekSelectorForm');

    weekTypeSelect.addEventListener('change', function() {
        if (this.value === 'single') {
            singleWeekSection.style.display = 'block';
            rangeWeekSection.style.display = 'none';
        } else {
            singleWeekSection.style.display = 'none';
            rangeWeekSection.style.display = 'block';
        }
    });

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        let weekParam = '';
        
        if (weekTypeSelect.value === 'single') {
            const singleWeek = document.getElementById('singleWeek').value;
            if (!singleWeek || singleWeek < 1 || singleWeek > 53) {
                alert('Veuillez saisir un numéro de semaine valide (1-53)');
                return;
            }
            weekParam = singleWeek;
        } else {
            const startWeek = document.getElementById('startWeek').value;
            const endWeek = document.getElementById('endWeek').value;
            
            if (!startWeek || !endWeek || startWeek < 1 || startWeek > 53 || endWeek < 1 || endWeek > 53) {
                alert('Veuillez saisir des numéros de semaines valides (1-53)');
                return;
            }
            
            if (parseInt(startWeek) > parseInt(endWeek)) {
                alert('La semaine de début doit être inférieure ou égale à la semaine de fin');
                return;
            }
            
            weekParam = startWeek + '-' + endWeek;
        }

        window.location.href = '{{ path("weekly_report", {"weekNumber": "WEEK_PARAM"}) }}'.replace('WEEK_PARAM', weekParam);
    });
});
</script>
{% endblock %}
