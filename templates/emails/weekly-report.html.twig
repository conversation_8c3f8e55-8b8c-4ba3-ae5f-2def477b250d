<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
<title>Rapport Hebdomadaire ITRoom - {{ weekDisplayText|default('Semaine ' ~ weekNumber) }} ({{ startDate }} - {{ endDate }})</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        h1, h2 {
            color: #0056b3;
        }
        .summary-info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f4f4f4;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .red {
            color: red;
        }
        .green {
            color: green;
        }
        .fully-justified {
            color: #6c757d;
            font-style: italic;
        }
        .separator {
            border-bottom: 2px solid #000;
        }
        .exergue {
            font-weight: bold;
            background-color: rgb(240, 240, 240) !important;
        }
    </style>
</head>
<body>
    <h1>Rapport Hebdomadaire ITRoom - {{ weekDisplayText|default('Semaine ' ~ weekNumber) }} ({{ startDate }} - {{ endDate }})</h1>

    <div class="summary-info">
        <p><strong>Utilisateurs dans le rapport :</strong> {{ usersInReport }} sur {{ totalActiveUsers }} utilisateurs actifs non en régie ({{ (usersInReport / totalActiveUsers * 100)|round(1) }}%)</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Nom</th>
                <th>Prénom</th>
                <th>Client</th>
                <th>Projet</th>
                <th>Temps passé</th>
            </tr>
        </thead>
        <tbody>
            {% set tempsTravaille = 0 %}
            {% set tempsSurSite = 0 %}
            {% set currentUser = '' %}
            {% set isUnderDeclared = false %}
            {% set isFullyJustified = false %}

            {% for row in weeklyReport %}
                {% set rowClass = '' %}
                {% set userKey = row.nom ~ '-' ~ row.prenom %}
                
                {# Détection d'un nouvel utilisateur #}
                {% if currentUser != userKey and row.client != 'Temps déclaré (hors absence justifiée)' and row.client != 'Temps sur site' %}
                    {% set currentUser = userKey %}
                    {% set tempsTravaille = 0 %}
                    {% set tempsSurSite = 0 %}
                    {% set isUnderDeclared = false %}
                    {% set isFullyJustified = false %}
                {% endif %}

                {# Traitement des lignes de temps déclaré #}
                {% if row.client == 'Temps déclaré (hors absence justifiée)' %}
                    {% set tempsTravaille = row.temps_passe %}
                    {% set tempsTravailleNumeric = (tempsTravaille|replace({'h': ''}))|number_format(2, '.', '') %}
                    
                    {# Récupérer le temps sur site pour cet utilisateur #}
                    {% set tempsSurSite = '' %}
                    {% set found = false %}
                    {% for siteRow in weeklyReport %}
                        {% if not found and siteRow.nom == row.nom and siteRow.prenom == row.prenom and siteRow.client == 'Temps sur site' %}
                            {% set tempsSurSite = siteRow.temps_passe %}
                            {% set found = true %}
                        {% endif %}
                    {% endfor %}
                    
                    {% if tempsSurSite != '' %}
                        {% set tempsSurSiteNumeric = (tempsSurSite|replace({'h': ''}))|number_format(2, '.', '') %}
                        {% set isFullyJustified = tempsSurSiteNumeric == 0 %}
                        {% set isUnderDeclared = tempsTravailleNumeric < tempsSurSiteNumeric %}
                        
                        {% if isFullyJustified %}
                            {% set rowClass = 'fully-justified exergue' %}
                        {% else %}
                            {% set textColorClass = isUnderDeclared ? 'red' : 'green' %}
                            {% set rowClass = textColorClass ~ ' exergue' %}
                        {% endif %}
                    {% endif %}
                {% endif %}

                {# Traitement des lignes de temps sur site #}
                {% if row.client == 'Temps sur site' %}
                    {% set tempsSurSiteNumeric = (row.temps_passe|replace({'h': ''}))|number_format(2, '.', '') %}
                    
                    {% if isFullyJustified %}
                        {% set rowClass = 'fully-justified exergue separator' %}
                    {% else %}
                        {% set textColorClass = isUnderDeclared ? 'red' : 'green' %}
                        {% set rowClass = textColorClass ~ ' exergue separator' %}
                    {% endif %}
                {% endif %}

                {# Traitement des lignes de total global #}
                {% if row.prenom == 'Déclaré' %}
                    {% set tempsTravaille = row.temps_passe %}
                    {% set tempsTravailleNumeric = (tempsTravaille|replace({'h': ''}))|number_format(2, '.', '') %}
                    {% set totalExpectedNumeric = (totalExpected|replace({'h': ''})) %}
                    {% set globalColorClass = (tempsTravailleNumeric < totalExpectedNumeric ? 'red' : 'green') %}
                    {% set rowClass = globalColorClass ~ ' exergue' %}
                {% endif %}

                {% if row.prenom == 'Attendu' %}
                    {% set tempsTravailleNumeric = (tempsTravaille|replace({'h': ''}))|number_format(2, '.', '') %}
                    {% set tempsPasseNumeric = (row.temps_passe|replace({'h': ''}))|number_format(2, '.', '') %}
                    {% set rowClass = globalColorClass ~ ' exergue' %}
                {% endif %}

                {# Traitement de la ligne de pourcentage #}
                {% if row.prenom == 'Temps déclaré vs attendu' %}
                    {% set percentageValue = row.temps_passe|replace({'%': ''})|number_format(1, '.', '') %}
                    {% set percentageColorClass = (percentageValue < 100 ? 'red' : 'green') %}
                    {% set rowClass = percentageColorClass ~ ' separator exergue' %}
                {% endif %}

                {# Afficher la ligne avec la classe calculée #}
                <tr class="{{ rowClass }}">
                    <td>{{ row.nom }}</td>
                    <td>{{ row.prenom }}</td>
                    <td>{{ row.client }}</td>
                    <td>{{ row.projet }}</td>
                    <td>{{ row.temps_passe }}</td>
                </tr>
            {% else %}
                <tr>
                    <td colspan="5" style="text-align: center;">Aucune donnée disponible</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mettre en évidence les lignes de total
            const rows = document.querySelectorAll('table tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 2) {
                    const clientCell = cells[2];
                    if (clientCell.textContent.includes('Temps déclaré (hors absence justifiée)')) {
                        row.classList.add('temps-declare');
                    }
                    if (clientCell.textContent.includes('Temps sur site')) {
                        row.classList.add('temps-site');
                    }
                }
            });
        });
    </script>
</body>
</html>
