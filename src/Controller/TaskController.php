<?php

namespace App\Controller;

use App\Entity\Task;
use App\Entity\Project;
use App\Entity\User;
use App\Form\TaskType;
use App\Form\TimerTaskType;
use App\Repository\TaskRepository;
use App\Service\TaskManagerService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use App\Validator\Constraints\JourFerieValidator;
use Symfony\Component\HttpFoundation\Session\Session;
use App\Validator\Constraints\JourFerie;
use App\Service\WeeklyReportService;

/**
 * @Route("/task")
 */
class TaskController extends AbstractController
{
    /**
     * @Route("/", name="task_index", methods={"GET"})
     */
    public function index(TaskRepository $taskRepository): Response
    {
        $tasks = $taskRepository->getLastTasks();

        return $this->render('task/index.html.twig', [
            'msg' => 'Toutes les interventions d\'aujourd\'hui et passées (pas à venir)',
            'tasks' => $tasks,
        ]);
    }

    /**
     * @Route("/new", name="task_new", methods={"GET","POST"})
     * @param Request $request
     * @param TaskManagerService $taskManagerService
     * @param EntityManagerInterface $entityManager
     * @return Response
     * @throws \Exception
     */
    public function new(Request $request, TaskManagerService $taskManagerService, EntityManagerInterface $entityManager): Response
    {
        if (null === $this->getUser()->getCompany()) {
            $this->addFlash('error', 'Suite à une mise à jour vous devez choisir une entité pour rentrer une intervention');
            return $this->redirectToRoute('user_edit', ['id' => $this->getUser()->getId()]);
        }
        $task = new Task();
        $form = $this->createForm(TaskType::class, $task, [
            'project_description_url' => $this->generateUrl('project_description', [], UrlGeneratorInterface::ABSOLUTE_URL),
        ]);

        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) 
        {
            $task->setCreatedAt(new \DateTime());

            if (null === $task->getUser()) {
                $task->setUser($this->getUser());
            }
            if (true === $form->get('durationJ')->getData()) {
                $task->setDuration(7);
            }

            $entityManager->persist($task);

            if ($form->get('repeat')->getData() > 0) {
                $duplicatedTasks = $taskManagerService->duplicateTask($task, $form->get('repeat')->getData());
                foreach ($duplicatedTasks as $duplicatedTask) {
                    $entityManager->persist($duplicatedTask);
                }
            }

            if($form->get('subject')->getData() === null){
                $task->setSubject(" ");
            }

            if($form->get('project')->getData() === null){
                $project = $this->getDoctrine()
                                ->getRepository(Project::class)
                                ->findOneBy(['name'=>'aucun (AUCUN)']);

                if($project!==null){
                    $task->setProject($project);
                }     
            }

            $entityManager->flush();
            $this->addFlash('success', 'Sauvegarde effectuée avec succès');
            return $this->redirectToRoute('task_index');
        }


        return $this->render('task/new.html.twig', [
            'task' => $task,
            'form' => $form->createView(),
        ]); 
    }


    /**
     * @Route("/new-index", name="task_new_index", methods={"GET","POST"})
     * @param Request $request
     * @param TaskManagerService $taskManagerService
     * @param EntityManagerInterface $entityManager
     * @return Response
     * @throws \Exception
     */
    public function newIndex(Request $request, TaskManagerService $taskManagerService, EntityManagerInterface $entityManager): Response
    {
        $task = new Task();
        $form = $this->createForm(TimerTaskType::class, $task);

        $form->handleRequest($request);

        return $this->render('task/new.html.twig', [
            'task' => $task,
            'form' => $form->createView(),
        ]); 
    }

    /**
     * @Route("/{id}", name="task_show", methods={"GET"})
     */
    public function show(Task $task): Response
    {
        return $this->render('task/show.html.twig', [
            'task' => $task,
        ]);
    }

    /**
     * @Route("/{id}/edit", name="task_edit", methods={"GET","POST"})
     */
    public function edit(Request $request, Task $task): Response
    {
        if (null === $this->getUser()->getCompany()) {
            $this->addFlash('error', 'Suite à une mise à jour vous devez choisir une entité pour modifier une intervention');
            return $this->redirectToRoute('user_edit', ['id' => $this->getUser()->getId()]);
        }

        $form = $this->createForm(TaskType::class, $task, [
            'project_description_url' => $this->generateUrl('project_description', [], UrlGeneratorInterface::ABSOLUTE_URL),
        ]);
      
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            if ( $form->has('durationJ') &&  (int)$form->get('durationJ')->getData() == True ) {
                $task->setDuration(7);
            }
            $this->getDoctrine()->getManager()->flush();

            return $this->redirectToRoute('calendar', [
                'id_user' => $task->getUser()->getId(),
            ]);
        }

        return $this->render('task/edit.html.twig', [
            'task' => $task,
            'form' => $form->createView(),
        ]);
    }

    /**
     * @Route("/{id}", name="task_delete", methods={"DELETE"})
     */
    public function delete(Request $request, Task $task): Response
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            if ($this->isCsrfTokenValid('delete' . $task->getId(), $request->request->get('_token'))) {
                $entityManager = $this->getDoctrine()->getManager();
                $entityManager->remove($task);
                $entityManager->flush();
            }
        }

        return $this->redirectToRoute('task_index');
    }

    /**
     * @Route("/change/project/{id}" , name="change_project" )
     */
    public function changeProject(Request $request, TaskManagerService $taskManagerService, EntityManagerInterface $em, Project $project)
    {
        $tasks = [];
        $proj = $em->getRepository(Project::class)->findOneBy(['id' => $request->request->get('list_project')['name']]);

        if ($request->request->get('tasks') !== null ) {

            foreach ($request->request->get('tasks') as $idTask) {
                $task = $em->getRepository(Task::class)->findOneBy(['id' => $idTask]);
                $tasks[] = $task;

            }
            $taskManagerService->changeProject($tasks,$proj,$project);

        } else if ($request->request->get('ids') !== null ) {

            foreach ($request->request->get('ids') as $id) {

                $taskss = $this->getDoctrine()->getManager()->getRepository(Task::class)
                    ->findBy(['project' => $project->getId(), 'user' => $id]);

                foreach ($taskss as $task) {
                    $tasks[] = $task;
                }
            }

            $taskManagerService->changeProject($tasks,$proj,$project);
        }

        return $this->redirectToRoute('project_show',[
            'id' => $project->getId(),
        ]);
    }

    /**
     * @Route("/rapport-hebdo", name="weekly_report_selector", methods={"GET"})
     * @return Response
     */
    public function weeklyReportSelector(): Response
    {
        // Obtenir la semaine actuelle
        $currentWeek = (int)(new \DateTime())->format('W');

        return $this->render('task/weekly_report_selector.html.twig', [
            'currentWeek' => $currentWeek,
        ]);
    }

    /**
     * @Route("/rapport-hebdo/{weekNumber}", name="weekly_report", methods={"GET"})
     * @param string $weekNumber
     * @param WeeklyReportService $weeklyReportService
     * @return Response
     */
    public function getWeeklyReport(string $weekNumber, WeeklyReportService $weeklyReportService): Response
    {
        // Vérifier si c'est une plage de semaines (ex: "24-26") ou une seule semaine (ex: "25")
        if (strpos($weekNumber, '-') !== false) {
            // Plage de semaines
            [$startWeek, $endWeek] = explode('-', $weekNumber, 2);
            $startWeek = (int)$startWeek;
            $endWeek = (int)$endWeek;

            // Validation des numéros de semaines
            if ($startWeek < 1 || $startWeek > 53 || $endWeek < 1 || $endWeek > 53 || $startWeek > $endWeek) {
                throw $this->createNotFoundException('Numéros de semaines invalides');
            }

            // Calculer les dates de début et fin pour la plage
            $startDate = new \DateTime();
            $startDate->setISODate((int)$startDate->format('o'), $startWeek, 1)
                      ->setTime(0, 0, 0);

            $endDate = new \DateTime();
            $endDate->setISODate((int)$endDate->format('o'), $endWeek, 5)
                    ->setTime(23, 59, 59);

            $weekDisplayText = "Semaines $startWeek à $endWeek";
            $weekNumberForTemplate = "$startWeek-$endWeek";
        } else {
            // Semaine unique
            $singleWeek = (int)$weekNumber;

            // Validation du numéro de semaine
            if ($singleWeek < 1 || $singleWeek > 53) {
                throw $this->createNotFoundException('Numéro de semaine invalide');
            }

            $startDate = new \DateTime();
            $startDate->setISODate((int)$startDate->format('o'), $singleWeek, 1)
                      ->setTime(0, 0, 0);
            $endDate = clone $startDate;
            $endDate->modify('+4 days')->setTime(23, 59, 59);

            $weekDisplayText = "Semaine $singleWeek";
            $weekNumberForTemplate = $singleWeek;
        }

        $weeklyReportData = $weeklyReportService->getWeeklyReportForDateRange($startDate, $endDate);

        if ($this->getParameter('render_html')) {
            $html = $this->renderView('emails/weekly-report.html.twig', [
                'weeklyReport' => $weeklyReportData['report'],
                'totalWorked' => $weeklyReportData['totalWorked'],
                'totalExpected' => $weeklyReportData['totalExpected'],
                'weekNumber' => $weekNumberForTemplate,
                'weekDisplayText' => $weekDisplayText,
                'startDate' => $startDate->format('d/m/Y'),
                'endDate' => $endDate->format('d/m/Y'),
                'usersInReport' => $weeklyReportData['usersInReport'],
                'totalActiveUsers' => $weeklyReportData['totalActiveUsers'],
            ]);

            return new Response($html, 200, ['Content-Type' => 'text/html']);
        }

        return $this->json($weeklyReportData);
    }
}
