<?php

namespace App\Controller;

use App\Repository\CongeRepository;
use App\Entity\Conge;
use App\Entity\Task;
use App\Form\CongeType;
use App\Form\AdminCongeType;
use App\Form\CalendarCongeType;
use App\Form\RefusCongeType;
use App\Repository\TaskRepository;
use App\Service\UserManagerService;
use Exception;
use Swift_Mailer;
use Swift_Message;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\ORM\EntityManagerInterface;
use App\Service\ExportCongeCsvService;
use App\Service\CongeManagerService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

class CongeController extends AbstractController
{

    public const ACCOUNTING_MAIL = '<EMAIL>';

    /**
     * @Route("/conge/dashboard", name="conge_dashboard", methods={"GET"})
     * @param CongeRepository $congeRepository
     * @return Response
     */
    public function dashboard(CongeRepository $congeRepository): Response
    {
        $congesEnAttente = $congeRepository->getCongeEnAttente();
        $congeDuJour = $congeRepository->findCongeDuJour(date("Y-m-d"));
        $congeAVenir = $congeRepository->getCongesAVenir();

        return $this->render('conge/dashboard.html.twig', [
            'congesEnAttente' => $congesEnAttente,
            'congeDuJour' => $congeDuJour,
            'congeAVenir' => $congeAVenir
        ]);
    }

    /**
     * @Route("/conge/calendrier", name="conge_calendrier", methods={"GET","POST"})
     * @param CongeRepository $congeRepository
     * @param CongeManagerService $congeManagerService
     * @param Request $request
     * @return Response
     */
    public function calendrier(CongeRepository $congeRepository , CongeManagerService $congeManagerService , Request $request): Response
    {
        $conges = $congeRepository->getTousLesConges();
        $form = $this->createForm(CalendarCongeType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $conges = [];
            $data = $form->getData();

            foreach ($data['users'] as $user ){
                $congesUser = $congeManagerService->objetsEnTableau($user->getConges()->getValues());
                $conges = array_merge($conges,$congesUser);
            }
        }

        return $this->render('conge/calendrier.html.twig', [
            'conges' => $conges,
            'form' => $form->createView()
        ]);
    }

    /**
     * @Route("/conge/validation", name="conge_validation", methods={"GET"})
     *
     * @IsGranted("ROLE_GESTION_CONGES")
     * @param CongeRepository $congeRepository
     * @return Response
     */
    public function validation(CongeRepository $congeRepository): Response
    {
        $congesEnAttente = $congeRepository->getCongeEnAttente();

        return $this->render('conge/validation.html.twig', [
        'congesEnAttente' => $congesEnAttente
        ]);
    }

    /**
     * @Route("/conge/{date}", name="conge_index", methods={"GET","POST"} )
     * @param CongeRepository $congeRepository
     * @param Request $request
     * @param EntityManagerInterface $entityManager
     * @param string|null $date
     * @return Response
     * @throws Exception
     */
    public function index(
        CongeRepository $congeRepository,
        Request $request,
        EntityManagerInterface $entityManager,
        string $date = null
        ): Response
    {
        $openForm = false;
        $conge = new Conge($this->getUser());
        if (null !== $date){
            $dateD = new \DateTime($date);
            $conge->setDateDebut($dateD);
            $openForm = true;
        }

        $form = $this->createForm(CongeType::class, $conge);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($conge);
            $entityManager->flush();

            $openForm=false;
        }

        $conges = $congeRepository->getCongesParUser($this->getUser());

        return $this->render('conge/index.html.twig', [
            'conges' => $conges,
            'form' => $form->createView(),
            'open_form' => $openForm
        ]);
    }

    /**
     * @Route("/conge/admin/new", name="conge_admin_new", methods={"GET","POST"})
     * @IsGranted("ROLE_ADMIN")
     * @param Request $request
     * @param EntityManagerInterface $entityManager
     * @return Response
     */
    public function adminNew(Request $request, EntityManagerInterface $entityManager): Response
    {
        $conge = new Conge($this->getUser()); // Temporaire, sera remplacé par l'utilisateur sélectionné
        $form = $this->createForm(AdminCongeType::class, $conge);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // L'utilisateur sélectionné remplace l'utilisateur temporaire
            $selectedUser = $form->get('user')->getData();
            $conge->setUser($selectedUser);

            $entityManager->persist($conge);
            $entityManager->flush();

            $this->addFlash('success', 'Congé créé avec succès pour ' . $selectedUser->getFirstName() . ' ' . $selectedUser->getName());
            return $this->redirectToRoute('conge_index');
        }

        return $this->render('conge/admin_new.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    /**
     * @Route("/edit/{id}", name="conge_edit", methods={"GET","POST"})
     * @param Request $request
     * @param Conge $conge
     * @param TaskRepository $taskRepository
     * @return Response
     */
    public function edit(Request $request, Conge $conge, TaskRepository $taskRepository, UserManagerService $userManagerService, Swift_Mailer $mailer): Response
    {
        if (!$this->isGranted('ROLE_GESTION_CONGES') && $this->getUser() !== $conge->getUser()) {
            return $this->redirectToRoute('conge_index');
        }
        $wasValid = false;

        $entityManager = $this->getDoctrine()->getManager();

        if ("Validé" === $conge->getEtat()) {

            //Useful for mailing
            $debutInitial = $conge->getDateDebut();
            $finInitiale = $conge->getDateFin();

            $tasks = $taskRepository->getTasksByConge($conge);
            foreach ($tasks as $task) {
                $entityManager->remove($task);
            }

            $wasValid = true;
        }

        $form = $this->createForm(CongeType::class, $conge);
        $form->handleRequest($request);
        $conge->setEtat(Conge::ETATS['En Attente']);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            // Si les congés avaient été validés, envoie un mail.
            if ($wasValid === true) { // Send mail to all users with ROLE_GESTION_CONGES roles
                $recipents = $userManagerService->findAllRole("ROLE_GESTION_CONGES");

                $message = (new Swift_Message('Modification congés'))
                        ->setFrom('<EMAIL>')
                        ->setTo($recipents)
                        ->setBody(
                            $this->renderView(
                                'emails/modification-conge.html.twig', [
                                'name' => $conge->getUser()->getFirstName(),
                                'dateD' => $conge->getDateDebut(),
                                'dateF' => $conge->getDateFin(),
                                'debutInit' => $debutInitial,
                                'finInit' => $finInitiale,
                            ]));

                $mailer->send($message);
            }

            return $this->redirectToRoute('conge_index', [
                'id' => $conge->getId(),
            ]);
        }

        return $this->render('conge/edit.html.twig', [
            'conge' => $conge,
            'form' => $form->createView(),
        ]);
    }

    /**
     * @Route("/conge/delete/{id}", name="conge_delete", methods={"GET","DELETE"})
     * @param Request $request
     * @param Conge $conge
     * @param TaskRepository $taskRepository
     * @return Response
     */
    public function delete(Request $request, Conge $conge,TaskRepository $taskRepository, Swift_Mailer $mailer, UserManagerService $userManagerService): Response
    {
        if (!$this->isGranted('ROLE_GESTION_CONGES') && $this->getUser() !== $conge->getUser()) {
            return $this->redirectToRoute('conge_index');
        }

        $entityManager = $this->getDoctrine()->getManager();

        if ("Validé" === $conge->getEtat()) {

            $tasks = $taskRepository->getTasksByConge($conge);

            foreach ($tasks as $task) {
                $entityManager->remove($task);
            }

            //Send mail to administration
            $recipents = $userManagerService->findAllRole("ROLE_GESTION_CONGES");
            $adminMessage = (new Swift_Message('Annulation congé'))->setFrom('<EMAIL>')
                ->setTo($recipents)
                ->setBody(
                    $this->renderView(
                        'emails/annulation-conge-admin.html.twig', [
                        'name' => $conge->getUser()->getFirstName(),
                        'dateD' => $conge->getDateDebut(),
                        'dateF' => $conge->getDateFin()
                    ]));
            $mailer->send($adminMessage);
        }
        $entityManager->remove($conge);
        $entityManager->flush();

        return $this->redirectToRoute('conge_index');
    }

    /**
     * @Route("/conge-csv/{annee}/{mois}", name="conge_csv", methods={"GET"})
     *
     * @IsGranted("ROLE_GESTION_CONGES")
     * @param ExportCongeCsvService $exportCsv
     * @param CongeRepository $congeRepository
     * @param int $annee
     * @param int|null $mois
     * @return BinaryFileResponse
     */
    public function csv(ExportCongeCsvService $exportCsv, CongeRepository $congeRepository, int $annee, int $mois=null)
    {
        if ($mois !== null){
            $conges = $congeRepository->getCongeParAnneeEtMois($annee,$mois);
        } else {
            $conges = $congeRepository->getCongeParAnnee($annee);
        }

       return $exportCsv->generateCsv($conges);
    }

    /**
     * @Route("/valide/{id}", name="conge_validate", methods={"GET","POST"})
     *
     * @IsGranted("ROLE_GESTION_CONGES")
     * @param Conge $conge
     * @param CongeManagerService $congeManagerService
     * @param Swift_Mailer $mailer
     * @return Response
     * @throws Exception
     */
    public function validerConge(Conge $conge, CongeManagerService $congeManagerService, Swift_Mailer $mailer, UserManagerService $userManagerService): Response
    {
        if ( $conge->getEtat() !== Conge::ETATS['En Attente']){
            throw new AccessDeniedException;
        }

        if ($conge->getDateDebut()->getTimestamp() < strtotime('today midnight')) {
            $message = (new Swift_Message('Congé saisi en retard'))
                ->setFrom('<EMAIL>')
                ->setTo(self::ACCOUNTING_MAIL)
                ->setBody(
                    $this->renderView(
                        'emails/previous_conge.html.twig', [
                        'name' => $conge->getUser()->getFirstName(),
                        'dateD' => $conge->getDateDebut(),
                        'dateF' => $conge->getDateFin(),
                        'timeOfDayD' => $conge->getDureeDebut(),
                        'timeOfDayF' => $conge->getDureeFin()
                    ]));
            $mailer->send($message);
        }

        $timestampConge = $conge->getDateDebut()->getTimestamp();

        if ($timestampConge > strtotime('now') && $timestampConge < strtotime('+7 days')) {
            $message = (new Swift_Message('Congé en last minute !'))
                ->setFrom('<EMAIL>')
                ->setTo(self::ACCOUNTING_MAIL)
                ->setBody(
                    $this->renderView(
                        'emails/last-minute-conge.html.twig', [
                        'name' => $conge->getUser()->getFirstName(),
                        'dateD' => $conge->getDateDebut(),
                        'dateF' => $conge->getDateFin(),
                        'timeOfDayD' => $conge->getDureeDebut(),
                        'timeOfDayF' => $conge->getDureeFin()
                    ]));
            $mailer->send($message);
        }

        $mail = $conge->getUser()->getMail();
        $lesTaches = $congeManagerService->createTasksFromConge($conge);
        $message = (new Swift_Message('Validation congé'))
            ->setFrom('<EMAIL>')
            ->setTo($mail)
            ->setBody(
                $this->renderView(
                    'emails/confirmation-conge.html.twig', [
                    'name' => $conge->getUser()->getFirstName(),
                    'dateD' => $conge->getDateDebut(),
                    'dateF' => $conge->getDateFin()
                ]));

        $mailer->send($message);

        //Validation Mail for administration only
        $recipents = $userManagerService->findAllRole("ROLE_GESTION_CONGES");
        $adminMessage = (new Swift_Message('Validation congé'))->setFrom('<EMAIL>')
            ->setTo($recipents)
            ->setBody(
                $this->renderView(
                    'emails/confirmation-conge-admin.html.twig', [
                    'name' => $conge->getUser()->getFirstName(),
                    'dateD' => $conge->getDateDebut(),
                    'dateF' => $conge->getDateFin()
                ]));
        $mailer->send($adminMessage);

        $conge->setEtat(Conge::ETATS['Validé']);
        $this->getDoctrine()->getManager()->flush();

        return $this->redirectToRoute('conge_validation');
    }

    /**
     * @Route("/refuse/{id}", name="conge_refuse", methods={"GET","POST"})
     *
     * @IsGranted("ROLE_GESTION_CONGES")
     * @param Conge $conge
     * @param Swift_Mailer $mailer
     * @param Request $request
     * @return Response
     */
    public function refuserConge(Conge $conge, Swift_Mailer $mailer, Request $request): Response
    {
        if ( $conge->getEtat() !== Conge::ETATS['En Attente']){
            throw new AccessDeniedException;
        }
       
        $form = $this->createForm(RefusCongeType::class, $conge);
        $form->handleRequest($request);
        $conge->setEtat(Conge::ETATS['Refusé']);
        if($form->isSubmitted() && $form->isValid()){
            
            $this->getDoctrine()->getManager()->flush();

            $mail = $conge->getUser()->getMail();
            //mail de refus
            $message = (new Swift_Message('Refus congé'))
                ->setFrom('<EMAIL>')
                ->setTo($mail)
                ->setBody(
                    $this->renderView(
                        'emails/refus-conge.html.twig', [
                        'name' => $conge->getUser()->getFirstName(),
                        'dateD' => $conge->getDateDebut(),
                        'dateF' => $conge->getDateFin(),
                        'messageRefus' => $conge->getMessagerefus()
                    ]));
                    
            $mailer->send($message);
            return $this->redirectToRoute('conge_validation');
            }
        
        return $this->render('conge/refus.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}