<?php

namespace App\Form;

use App\Entity\Conge;
use App\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use App\Validator\Constraints\WeekEnd;
use App\Validator\Constraints\JourFerie;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class AdminCongeType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('user', EntityType::class, [
                'class' => User::class,
                'choice_label' => function (User $user) {
                    return $user->getFirstName() . ' ' . $user->getName();
                },
                'query_builder' => function ($repository) {
                    return $repository->createQueryBuilder('u')
                        ->where('u.actif = :actif')
                        ->orWhere('u.actif = 1')
                        ->setParameter('actif', 'oui')
                        ->orderBy('u.firstName, u.name');
                },
                'label' => 'Utilisateur',
                'placeholder' => 'Sélectionnez un utilisateur',
                'attr' => ['class' => 'form-control']
            ])
            ->add('dateDebut', DateType::class, [
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'empty_data' => (new \DateTime())->format('Y-m-d'),
                'constraints' => [new JourFerie, new WeekEnd],
                'label' => 'Date de début'
            ])
            ->add('dureeDebut', ChoiceType::class, [
                'choices' => [
                    'Journée complète' => 'journée complète',
                    'Matin' => 'matin',
                    'Après-midi' => 'apres-midi',
                ],
                'label' => 'Durée début'
            ])
            ->add('dateFin', DateType::class, [
                'widget' => 'single_text',
                'format' => 'yyyy-MM-dd',
                'empty_data' => (new \DateTime())->format('Y-m-d'),
                'constraints' => [new JourFerie, new WeekEnd],
                'label' => 'Date de fin'
            ])
            ->add('dureeFin', ChoiceType::class, [
                'choices' => [
                    'Journée complète' => 'journée complète',
                    'Matin' => 'matin',
                    'Après-midi' => 'apres-midi',
                ],
                'label' => 'Durée fin'
            ])
            ->add('reason', ChoiceType::class, [
                'choices' => Conge::REASON,
                'label' => 'Motif'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Conge::class,
        ]);
    }
}
