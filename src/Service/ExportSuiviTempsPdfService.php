<?php
/**
 * Created by PhpStorm.
 * User: glefer
 * Date: 11/03/19
 * Time: 07:49
 */

namespace App\Service;


use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Twig_Environment;

class ExportSuiviTempsPdfService
{
    /**
     * @var Twig_Environment
     */
    private $twig;

    public function __construct(Twig_Environment $twig)
    {
        $this->twig = $twig;
    }


    public function generatePdf($tasks, $filters, $cumulativeSearch = false)
    {
        $totalHeures = 0;
        foreach ($tasks as $task) {
            $totalHeures += $task['duration'];
        }
        $tmpfname = tempnam(sys_get_temp_dir(), 'sdt') . '.html';
        $clientName = '';
        if (!empty($filters['client'])){
            $clientName = $filters['client']  instanceof \Traversable ? iterator_to_array($filters['client']) : (array) $filters['client'] ;
            $clientName = join('/',$clientName);
        }
        $startDate = $filters['dateD'];
        $endDate = $filters['dateF'];
        $render = $this->twig->render('recherche/exportpdf/template.html.twig',
            [
                'client' => $clientName,
                'date_debut' => $startDate->format('d/m/Y'),
                'date_fin' => $endDate->format('d/m/Y'),
                'tasks' => $tasks,
                'total_heures' => number_format($totalHeures, 2, '.', ' '),
                'total_jours' => number_format($totalHeures / 7.5, 2, '.', ' '),
                'cumulativeSearch' => $cumulativeSearch
            ]);
        file_put_contents($tmpfname, $render);
        $pdfTmpfname = tempnam(sys_get_temp_dir(), 'pdf');
        $tmp = '';
        $tmp2 = [];
        exec('wkhtmltopdf ' . $tmpfname . ' ' . $pdfTmpfname, $tmp2, $tmp);
        $response = new BinaryFileResponse($pdfTmpfname);
        $response->headers->set('Content-Type', 'application/pdf');
        // Set content disposition inline of the file
        $fileName = $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d');
        if (!empty($clientName)) {
            $fileName .= '_' . str_replace(' ', '_', str_replace('/','-',$clientName));
        }
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $fileName . '.pdf'
        );

        return $response;
    }

}