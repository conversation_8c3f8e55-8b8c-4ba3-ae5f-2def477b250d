<?php
namespace App\Service;

use App\Repository\TaskRepository;
use App\Repository\CongeRepository;
use Twig\Environment;

/**
 * Service de génération et d'envoi des rapports hebdomadaires
 */
class WeeklyReportService
{
    /**
     * @var TaskRepository
     */
    private $taskRepository;

    /**
     * @var CongeRepository
     */
    private $congeRepository;

    /**
     * @var Environment
     */
    private $twig;

    /**
     * @var string
     */
    private $noReplyAddress;

    /**
     * @var \Swift_Mailer
     */
    private $mailer;

    /**
     * @var string
     */
    private $defaultRecipientAddress;

    /**
     * Liste des emails d'utilisateurs à exclure du rapport
     */
    private const EXCLUDE_USER_EMAIL = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    /**
     * Constructeur du service
     * Initialise les dépendances nécessaires pour générer et envoyer les rapports
     * 
     * @param TaskRepository $taskRepository Repository pour accéder aux tâches
     * @param CongeRepository $congeRepository Repository pour accéder aux congés
     * @param Environment $twig Service Twig pour le rendu des templates
     * @param string $noReplyAddress Adresse email d'expédition
     * @param \Swift_Mailer $mailer Service d'envoi d'emails
     * @param string $defaultRecipientAddress Adresse email de destination par défaut
     */
    public function __construct(
        TaskRepository $taskRepository,
        CongeRepository $congeRepository,
        Environment $twig,
        string $noReplyAddress,
        \Swift_Mailer $mailer,
        string $defaultRecipientAddress
    ) {
        $this->taskRepository = $taskRepository;
        $this->congeRepository = $congeRepository;
        $this->twig = $twig;
        $this->noReplyAddress = $noReplyAddress;
        $this->mailer = $mailer;
        $this->defaultRecipientAddress = $defaultRecipientAddress;
    }

    /**
     * Envoie le rapport hebdomadaire par email
     * Génère le rapport, prépare le contenu de l'email et l'envoie aux destinataires
     * 
     * @param array $recipients Liste des destinataires (optionnel)
     * @return void
     */
    public function sendWeeklyReportByEmail(array $recipients = []): void
    {
        $weeklyReportData = $this->getWeeklyReport();
        $subject = $this->generateEmailSubject();

        $defaultRecipientAddress = '<EMAIL>';
        $recipients = !empty($recipients) ? $recipients : [$defaultRecipientAddress];

        $weeklyReport = $weeklyReportData['report'];
        $totalWorked = $weeklyReportData['totalWorked'];
        $totalExpected = $weeklyReportData['totalExpected'];
        $usersInReport = $weeklyReportData['usersInReport'];
        $totalActiveUsers = $weeklyReportData['totalActiveUsers'];

        [$startDate, $endDate] = $this->getLastWeekDateRange();
        $weekNumber = (int)$startDate->format('W');

        $emailBody = $this->twig->render('emails/weekly-report.html.twig', [
            'weeklyReport' => $weeklyReport,
            'totalWorked' => $totalWorked,
            'totalExpected' => $totalExpected,
            'weekNumber' => $weekNumber,
            'startDate' => $startDate->format('d/m/Y'),
            'endDate' => $endDate->format('d/m/Y'),
            'usersInReport' => $usersInReport,
            'totalActiveUsers' => $totalActiveUsers,
        ]);

        $message = (new \Swift_Message($subject))
            ->setFrom($this->noReplyAddress)
            ->setTo($recipients)
            ->setBody($emailBody, 'text/html');

        $this->mailer->send($message);
    }

    /**
     * Génère le sujet de l'email pour le rapport hebdomadaire
     * Crée un sujet standardisé incluant la période couverte par le rapport
     * 
     * @return string
     */
    private function generateEmailSubject(): string
    {
        $startDate = $this->formatDate('last monday');
        $endDate = $this->formatDate('last saturday');
        return "[INTERNE] Rapport hebdomadaire du $startDate au $endDate";
    }

    /**
     * Formate une date selon le format français
     * Convertit une expression de date en format JJ/MM/AAAA
     * 
     * @param string $dateExpression Expression de date
     * @return string
     */
    private function formatDate(string $dateExpression): string
    {
        return (new \DateTime($dateExpression))->format('d/m/Y');
    }

    /**
     * Récupère le rapport hebdomadaire pour la semaine dernière
     * Détermine les dates de début et fin de la semaine dernière et génère le rapport
     * 
     * @return array
     */
    public function getWeeklyReport(): array
    {
        [$lastWeekMonday, $lastWeekFriday] = $this->getLastWeekDateRange();
        
        // Vérification supplémentaire pour s'assurer que les dates sont dans le bon ordre
        if ($lastWeekMonday > $lastWeekFriday) {
            $temp = $lastWeekMonday;
            $lastWeekMonday = $lastWeekFriday;
            $lastWeekFriday = $temp;
        }
        
        return $this->getWeeklyReportForDateRange($lastWeekMonday, $lastWeekFriday);
    }

    /**
     * Génère un rapport hebdomadaire pour une plage de dates spécifique
     * Collecte et organise les données de temps de travail pour tous les utilisateurs actifs
     *
     * @param \DateTime $startDate Date de début
     * @param \DateTime $endDate Date de fin
     * @return array
     */
    public function getWeeklyReportForDateRange(\DateTime $startDate, \DateTime $endDate): array
    {
        // Récupérer TOUS les utilisateurs actifs, pas seulement ceux avec des tâches
        // Ajouter le paramètre onSite = true pour exclure les utilisateurs en régie
        $allActiveUsers = $this->taskRepository->getAllActiveUsers(self::EXCLUDE_USER_EMAIL, true);
        $timeInputsPerProject = $this->taskRepository->getTimeEntriesPerUser($startDate, $endDate);
        $congesByUser = $this->getCongesByUser($startDate, $endDate);

        $report = [];
        $totalDeclared = 0;

        // Préparer la liste de tous les utilisateurs (avec ou sans tâches/congés)
        $allUsers = [];

        // Initialiser tous les utilisateurs actifs
        foreach ($allActiveUsers as $user) {
            $userKey = $user['firstName'] . ' ' . $user['name'];
            $allUsers[$userKey] = [
                'firstName' => $user['firstName'],
                'name' => $user['name'],
                'total' => 0,
                'weekly_work_time' => $user['weekly_work_time'] ?? 1.0,
                'has_tasks' => false,
                'has_conges' => isset($congesByUser[$userKey]),
                'itroom_time' => 0,
            ];
        }

        // Mettre à jour les utilisateurs avec des tâches
        foreach ($timeInputsPerProject as $entry) {
            $userKey = $entry['firstName'] . ' ' . $entry['lastName'];
            if (isset($allUsers[$userKey])) {
                $allUsers[$userKey]['total'] += $entry['total'] ?? 0;
                $allUsers[$userKey]['has_tasks'] = true;
                $allUsers[$userKey]['weekly_work_time'] = $entry['weekly_work_time'] ?? 1.0;

                // Calculer le temps imputé à IT Room (insensible à la casse) en excluant Ecole - Alternance
                if (stripos($entry['client_name'], 'IT Room') !== false &&
                    stripos($entry['project_name'], 'Ecole - Alternance') === false) {
                    $allUsers[$userKey]['itroom_time'] += $entry['total'] ?? 0;
                }
            }
        }

        // Traiter tous les utilisateurs
        foreach ($allUsers as $userKey => $user) {
            $combinedEntries = $this->getCombinedEntries($userKey, $timeInputsPerProject, $congesByUser);

            if (empty($combinedEntries) && !$user['has_conges']) {
                // Utilisateur sans tâches ni congés
                $report[] = [
                    'nom' => $user['name'],
                    'prenom' => $user['firstName'],
                    'client' => 'Aucune déclaration',
                    'projet' => '',
                    'temps_passe' => '0h',
                ];
            } else {
                // Utilisateur avec tâches ou congés
                $this->addUserEntriesToReport($userKey, $combinedEntries, $report);
            }

            $taskTotal = $this->calculateTotalByType($combinedEntries, 'task');
            $congesTotal = $this->calculateTotalByType($combinedEntries, 'conge');
            $this->addSummaryLines($userKey, $taskTotal, $congesTotal, $startDate, $endDate, $report, $user);

            $totalDeclared += $taskTotal + $congesTotal;
        }

        // Ajouter les lignes de résumé global
        $this->addGlobalSummaryLines($startDate, $endDate, $totalDeclared, $report);

        // Trier le rapport avec les modifications demandées
        $this->sortWeeklyReport($report);

        // Compter les utilisateurs uniques dans le rapport
        $uniqueUsers = [];
        foreach ($report as $line) {
            if (isset($line['nom']) && $line['nom'] !== 'Total' && $line['nom'] !== 'IT Room') {
                $userKey = $line['prenom'] . ' ' . $line['nom'];
                $uniqueUsers[$userKey] = true;
            }
        }
        $usersInReport = count($uniqueUsers);

        // Calculer le total attendu (somme des temps sur site)
        $totalOnSite = 0;
        foreach ($report as $line) {
            if (isset($line['client']) && $line['client'] === 'Temps sur site') {
                $totalOnSite += (float)str_replace('h', '', $line['temps_passe']);
            }
        }

        return [
            'report' => $report,
            'totalWorked' => $totalDeclared,
            'totalExpected' => $totalOnSite,
            'usersInReport' => $usersInReport,
            'totalActiveUsers' => count($allActiveUsers),
        ];
    }

    /**
     * Récupère la plage de dates de la semaine dernière
     * 
     * @return array Tableau contenant la date de début et la date de fin
     */
    private function getLastWeekDateRange(): array
    {
        // Obtenir le lundi de la semaine dernière
        $lastWeekMonday = new \DateTime('last monday');
        $lastWeekFriday = (new \DateTime('last friday'))->setTime(23, 59, 59);
        return [$lastWeekMonday, $lastWeekFriday];
    }

    /**
     * Récupère les congés pour tous les utilisateurs dans une plage de dates
     * Organise les congés par utilisateur et calcule leur durée effective
     * 
     * @param \DateTime $startDate Date de début
     * @param \DateTime $endDate Date de fin
     * @return array
     */
    private function getCongesByUser(\DateTime $startDate, \DateTime $endDate): array
    {
        $conges = $this->congeRepository->getCongesInDateRange($startDate, $endDate);
        $congesByUser = [];

        foreach ($conges as $conge) {
            $userKey = $conge['firstName'] . ' ' . $conge['name'];
            
            $adjustedStartDate = max($conge['dateDebut'], $startDate);
            $adjustedEndDate = min($conge['dateFin'], $endDate);
            
            $totalDays = $this->calculateTotalDays(
                $adjustedStartDate,
                $adjustedEndDate,
                $conge['dureeDebut'],
                $conge['dureeFin'],
                $conge['dateDebut'] < $startDate,
                $conge['dateFin'] > $endDate
            );

            $congesByUser[$userKey][] = [
                'total_days' => $totalDays,
                'etat' => $conge['etat'],
                'reason' => $conge['reason'] ?? 'Congé',
                'weekly_work_time' => $conge['weekly_work_time'] ?? 1.0,
            ];
        }

        return $congesByUser;
    }

    /**
     * Calcule le nombre total de jours de congés
     * Prend en compte les demi-journées et exclut les jours fériés et les week-ends
     *
     * @param \DateTime $startDate Date de début
     * @param \DateTime $endDate Date de fin
     * @param string $dureeDebut Type de durée pour le premier jour (journée complète, matin, après-midi)
     * @param string $dureeFin Type de durée pour le dernier jour (journée complète, matin, après-midi)
     * @param bool $isStartDateAdjusted Indique si la date de début a été ajustée
     * @param bool $isEndDateAdjusted Indique si la date de fin a été ajustée
     * @return float
     */
    private function calculateTotalDays(
        \DateTime $startDate,
        \DateTime $endDate,
        string $dureeDebut,
        string $dureeFin,
        bool $isStartDateAdjusted,
        bool $isEndDateAdjusted
    ): float {
        // Calculer le nombre de jours ouvrés (excluant week-ends et jours fériés)
        $workingDays = $this->countWorkingDaysInRange($startDate, $endDate);

        if ($isStartDateAdjusted && $isEndDateAdjusted) {
            return $workingDays;
        }

        if ($isStartDateAdjusted) {
            // Si la date de fin a été ajustée et que c'est une demi-journée matin, soustraire 0.5
            if ($dureeFin === 'matin' && $this->isWorkingDay($endDate, $this->getHolidays())) {
                $workingDays -= 0.5;
            }
            return max(0, $workingDays);
        }

        if ($isEndDateAdjusted) {
            // Si la date de début a été ajustée et que c'est une demi-journée après-midi, soustraire 0.5
            if ($dureeDebut === 'apres-midi' && $this->isWorkingDay($startDate, $this->getHolidays())) {
                $workingDays -= 0.5;
            }
            return max(0, $workingDays);
        }

        // Cas normal : ajuster selon les demi-journées
        if ($workingDays === 1) {
            $workingDays = $dureeDebut === 'journée complète' ? 1 : 0.5;
        } else if ($workingDays > 1) {
            // Ajuster pour les demi-journées de début et fin
            if ($dureeDebut === 'apres-midi' && $this->isWorkingDay($startDate, $this->getHolidays())) {
                $workingDays -= 0.5;
            }
            if ($dureeFin === 'matin' && $this->isWorkingDay($endDate, $this->getHolidays())) {
                $workingDays -= 0.5;
            }
        }

        return max(0, $workingDays);
    }

    /**
     * Compte le nombre de jours ouvrés dans une plage de dates
     * Exclut les week-ends (samedi et dimanche) et les jours fériés
     *
     * @param \DateTime $startDate Date de début
     * @param \DateTime $endDate Date de fin
     * @return float
     */
    private function countWorkingDaysInRange(\DateTime $startDate, \DateTime $endDate): float {
        $holidays = $this->getHolidays();
        $workingDays = 0;

        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dayOfWeek = (int)$currentDate->format('N');
            $dateStr = $currentDate->format('Y-m-d');

            // Compter seulement les jours ouvrés (lundi à vendredi) qui ne sont pas fériés
            if ($dayOfWeek < 6 && !in_array($dateStr, $holidays)) {
                $workingDays++;
            }

            $currentDate->modify('+1 day');
        }

        return $workingDays;
    }

    /**
     * Compte le nombre de jours fériés dans une plage de dates
     * Utilise la liste des jours fériés pour déterminer combien sont dans la plage
     *
     * @param \DateTime $startDate Date de début
     * @param \DateTime $endDate Date de fin
     * @return int
     */
    private function countHolidaysInRange(\DateTime $startDate, \DateTime $endDate): int {
        $holidays = $this->getHolidays();
        $count = 0;

        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            if (in_array($currentDate->format('Y-m-d'), $holidays)) {
                $count++;
            }
            $currentDate->modify('+1 day');
        }

        return $count;
    }

    /**
     * Combine les entrées de temps et de congés pour un utilisateur
     * Fusionne les tâches et les congés en une seule liste d'entrées
     * 
     * @param string $userKey Identifiant de l'utilisateur
     * @param array $timeInputs Entrées de temps
     * @param array $congesByUser Congés par utilisateur
     * @return array
     */
    private function getCombinedEntries(string $userKey, array $timeInputs, array $congesByUser): array {
        $combinedEntries = [];
        
        $congeTasksFound = false;
        foreach ($timeInputs as $entry) {
            $entryKey = $entry['firstName'] . ' ' . $entry['lastName'];
            if ($entryKey === $userKey) {
                // Vérifier si c'est un projet d'école/alternance
                if ($this->isAlternanceProject($entry)) {
                    $combinedEntries[] = [
                        'type' => 'alternance',
                        'client_name' => $entry['client_name'],
                        'project_name' => $entry['project_name'],
                        'duration' => $entry['total'] ?? 0,
                    ];
                } 
                // Vérifier si c'est un projet de congé
                else if ($this->isCongeProject($entry)) {
                    $congeTasksFound = true;
                } 
                else {
                    $combinedEntries[] = [
                        'type' => 'task',
                        'client_name' => $entry['client_name'],
                        'project_name' => $entry['project_name'],
                        'duration' => $entry['total'] ?? 0,
                    ];
                }
            }
        }
        
        // Ensuite, ajouter les congés depuis la table conges
        if (isset($congesByUser[$userKey])) {
            foreach ($congesByUser[$userKey] as $conge) {
                $combinedEntries[] = [
                    'type' => 'conge',
                    'etat' => $conge['etat'] ?? 'Non spécifié',
                    'reason' => $conge['reason'] ?? 'Congé',  // Ajout de la raison du congé
                    'duration' => $conge['total_days'] * 7.5,
                ];
            }
        }
        // Si aucun congé n'a été trouvé dans la table conges mais qu'on a des tâches de congé,
        // c'est peut-être un ancien congé qui n'est plus dans la table conges
        else if ($congeTasksFound) {
            // Récupérer les tâches de congé pour cet utilisateur
            foreach ($timeInputs as $entry) {
                $entryKey = $entry['firstName'] . ' ' . $entry['lastName'];
                if ($entryKey === $userKey && $this->isCongeProject($entry)) {
                    $combinedEntries[] = [
                        'type' => 'conge',
                        'client_name' => $entry['client_name'],
                        'project_name' => $entry['project_name'],
                        'etat' => 'Congé',
                        'reason' => 'Congé',  // Par défaut pour les anciens congés
                        'duration' => $entry['total'] ?? 0,
                    ];
                }
            }
        }

        return $combinedEntries;
    }

    /**
     * Vérifie si un projet est un projet de congé
     * Détermine si le nom du client ou du projet contient le mot "Congés"
     * 
     * @param array $project Données du projet
     * @return bool
     */
    private function isCongeProject(array $project): bool {
        return stripos($project['client_name'], 'Congés') !== false;
    }

    /**
     * Vérifie si un projet est un projet d'alternance/école
     * Détermine si le nom du client ou du projet contient des mots-clés liés à l'alternance
     * 
     * @param array $project Données du projet
     * @return bool
     */
    private function isAlternanceProject(array $project): bool {
        return $project['project_name'] === 'Ecole - Alternance';
    }

    /**
     * Ajoute les entrées d'un utilisateur au rapport
     * Formate et ajoute les lignes pour chaque entrée de temps ou congé
     *
     * @param string $userKey Identifiant de l'utilisateur
     * @param array $combinedEntries Entrées combinées
     * @param array &$report Rapport à mettre à jour
     * @return void
     */
    private function addUserEntriesToReport(string $userKey, array $combinedEntries, array &$report): void
    {
        [$firstName, $lastName] = explode(' ', $userKey, 2);

        usort($combinedEntries, function ($a, $b) {
            return $b['duration'] <=> $a['duration'];
        });

        foreach ($combinedEntries as $entry) {
            if ($entry['type'] === 'task') {
                $report[] = $this->generateProjectLine($firstName, $lastName, $entry);
            } elseif ($entry['type'] === 'conge') {
                $report[] = $this->generateCongeLine($firstName, $lastName, $entry);
            } elseif ($entry['type'] === 'alternance') {
                $report[] = $this->generateLine($firstName, $lastName, $entry['client_name'], $entry['project_name'], $entry['duration']);
            }
        }
    }

    /**
     * Calcule le total des heures par type d'entrée
     * Somme les durées des entrées d'un type spécifique (tâche, congé, etc.)
     * 
     * @param array $combinedEntries Entrées combinées
     * @param string $type Type d'entrée à calculer
     * @return float
     */
    private function calculateTotalByType(array $combinedEntries, string $type): float {
        $total = 0;
        foreach ($combinedEntries as $entry) {
            if ($entry['type'] === $type) {
                $total += $entry['duration'];
            }
        }
        return $total;
    }

    /**
     * Ajoute les lignes de résumé pour un utilisateur
     * Calcule et ajoute les lignes de total déclaré et temps sur site
     *
     * @param string $userKey Clé de l'utilisateur
     * @param float $taskTotal Total des tâches
     * @param float $congesTotal Total des congés
     * @param \DateTime $startDate Date de début
     * @param \DateTime $endDate Date de fin
     * @param array &$report Rapport à mettre à jour
     * @param array $user Données de l'utilisateur
     * @return void
     */
    private function addSummaryLines(
        string $userKey,
        float $taskTotal,
        float $congesTotal,
        \DateTime $startDate,
        \DateTime $endDate,
        array &$report,
        array $user
    ): void {
        $alternanceTotal = 0;
        $combinedEntries = $this->getCombinedEntries($userKey, $this->taskRepository->getTimeEntriesPerUser($startDate, $endDate), $this->getCongesByUser($startDate, $endDate));
        foreach ($combinedEntries as $entry) {
            if ($entry['type'] === 'alternance') {
                $alternanceTotal += $entry['duration'];
            }
        }

        $totalDeclaredSansCongés = $taskTotal;

        // Calculer le temps sur site en jours
        $totalOnSiteDays = $this->calculateOnSiteTime($startDate, $endDate);

        // Soustraire les jours de congés et d'alternance
        $congesDays = $congesTotal / 7.5;
        $alternanceDays = $alternanceTotal / 7.5;
        $adjustedOnSiteDays = $totalOnSiteDays - ($congesDays + $alternanceDays);
        $adjustedOnSiteDays = max(0, $adjustedOnSiteDays); // Éviter les valeurs négatives

        [$firstName, $lastName] = explode(' ', $userKey, 2);

        // Appliquer le coefficient de temps partiel au temps sur site
        $weeklyWorkTime = $user['weekly_work_time'] ?? 1.0;
        $adjustedOnSiteHours = $adjustedOnSiteDays * 7.5 * $weeklyWorkTime;

        // Seulement deux lignes : temps déclaré (hors absence justifiée) et temps sur site
        $report[] = $this->generateSummaryLine($firstName, $lastName, 'Temps déclaré (hors absence justifiée)', $totalDeclaredSansCongés);
        $report[] = $this->generateSummaryLine($firstName, $lastName, 'Temps sur site', $adjustedOnSiteHours);
    }

    /**
     * Ajoute les lignes de résumé global au rapport
     * Calcule et ajoute les totaux déclarés et attendus pour l'ensemble des utilisateurs
     *
     * @param \DateTime $startDate Date de début
     * @param \DateTime $endDate Date de fin
     * @param float $totalDeclared Total des heures déclarées
     * @param array &$report Rapport à mettre à jour
     * @return void
     */
    private function addGlobalSummaryLines(\DateTime $startDate, \DateTime $endDate, float $totalDeclared, array &$report): void
    {
        // Calculer le total déclaré sans les congés et sans l'alternance
        $totalDeclaredSansCongés = 0;
        foreach ($report as $line) {
            if (isset($line['client']) && $line['client'] === 'Temps déclaré (hors absence justifiée)') {
                $totalDeclaredSansCongés += (float)str_replace('h', '', $line['temps_passe']);
            }
        }

        // Calculer le total attendu (somme des temps sur site)
        $totalOnSite = 0;
        foreach ($report as $line) {
            if (isset($line['client']) && $line['client'] === 'Temps sur site') {
                $totalOnSite += (float)str_replace('h', '', $line['temps_passe']);
            }
        }

        // Calculer le total IT Room en parcourant les données d'origine (en excluant Ecole - Alternance)
        $timeInputsPerProject = $this->taskRepository->getTimeEntriesPerUser($startDate, $endDate);
        $totalITRoom = 0;
        foreach ($timeInputsPerProject as $entry) {
            if (stripos($entry['client_name'], 'IT Room') !== false &&
                stripos($entry['project_name'], 'Ecole - Alternance') === false) {
                $totalITRoom += $entry['total'] ?? 0;
            }
        }

        // Calculer les pourcentages
        $itroomPercentage = $totalOnSite > 0 ? round(($totalITRoom / $totalOnSite) * 100, 1) : 0;
        $declaredPercentage = $totalOnSite > 0 ? round(($totalDeclaredSansCongés / $totalOnSite) * 100, 1) : 0;

        // Créer les lignes de résumé global avec heures et pourcentages
        $globalSummaryLines = [];
        $globalSummaryLines[] = $this->generateSummaryLine('IT Room', 'Total', '', $totalITRoom, $totalITRoom . 'h (' . $itroomPercentage . '%)');
        $globalSummaryLines[] = $this->generateSummaryLine('Déclaré', 'Total', '', $totalDeclaredSansCongés, $totalDeclaredSansCongés . 'h (' . $declaredPercentage . '%)');
        $globalSummaryLines[] = $this->generateSummaryLine('Attendu', 'Total', '', $totalOnSite);

        // Insérer les lignes de résumé global au début du rapport
        array_unshift($report, ...$globalSummaryLines);
    }

    /**
     * Vérifie si une date est un jour ouvré
     * Exclut les weekends et les jours fériés
     * 
     * @param \DateTime $date Date à vérifier
     * @param array $holidays Liste des jours fériés
     * @return bool
     */
    private function isWorkingDay(\DateTime $date, array $holidays): bool
    {
        $dayOfWeek = (int)$date->format('N');
        return $dayOfWeek < 6 && !$this->isHoliday($date, $holidays);
    }

    /**
     * Vérifie si une date est un jour férié
     * Compare la date avec la liste des jours fériés
     * 
     * @param \DateTime $date Date à vérifier
     * @param array $holidays Liste des jours fériés
     * @return bool
     */
    private function isHoliday(\DateTime $date, array $holidays): bool {
        return in_array($date->format('Y-m-d'), $holidays);
    }

    /**
     * Récupère la liste des jours fériés depuis un fichier CSV
     * Lit le fichier de jours fériés et enregistre des informations de débogage
     * 
     * @return array Liste des jours fériés au format Y-m-d
     */
    private function getHolidays(): array {
        $filePath = __DIR__ . '/../../jours-feries-seuls.csv';
        $holidays = [];

        if (file_exists($filePath)) {
            if (($handle = fopen($filePath, 'r')) !== false) {
                while (($data = fgetcsv($handle)) !== false) {
                    if (!empty($data[0])) {
                        $holidays[] = $data[0];
                    }
                }
                fclose($handle);
            }
            
            // Débogage - Écrire dans un fichier de log
            $logMessage = sprintf("Holidays file exists: %s\nHolidays count: %d\nFirst 5 holidays: %s\n", 
                $filePath, 
                count($holidays), 
                implode(', ', array_slice($holidays, 0, 5))
            );
            file_put_contents(__DIR__ . '/../../var/log/holidays_debug.log', $logMessage, FILE_APPEND);
        } else {
            // Débogage - Écrire dans un fichier de log
            $logMessage = sprintf("Holidays file does not exist: %s\n", $filePath);
            file_put_contents(__DIR__ . '/../../var/log/holidays_debug.log', $logMessage, FILE_APPEND);
        }

        return $holidays;
    }

    /**
     * Génère une ligne de projet pour le rapport
     * Formate les données d'un projet en ligne de rapport
     *
     * @param string $firstName Prénom de l'utilisateur
     * @param string $lastName Nom de l'utilisateur
     * @param array $project Données du projet
     * @return array
     */
    private function generateProjectLine(string $firstName, string $lastName, array $project): array {
        return $this->generateLine($firstName, $lastName, $project['client_name'], $project['project_name'], $project['duration']);
    }

    /**
     * Génère une ligne de congé pour le rapport
     * Formate les données d'un congé en ligne de rapport
     *
     * @param string $firstName Prénom de l'utilisateur
     * @param string $lastName Nom de l'utilisateur
     * @param array $conge Données du congé
     * @return array
     */
    private function generateCongeLine(string $firstName, string $lastName, array $conge): array {
        // Traduire la raison du congé (de l'anglais au français)
        $reasonKey = $conge['reason'] ?? '';
        $reasonTranslated = $this->translateCongeReason($reasonKey);

        $reason = !empty($reasonTranslated) ? ' - ' . $reasonTranslated : '';
        $etat = $conge['etat'] ?? 'Non spécifié';

        return $this->generateLine($firstName, $lastName, 'Congés', $etat . $reason, $conge['duration']);
    }
    
    /**
     * Traduit la raison du congé de l'anglais au français
     * Convertit les codes de raison stockés en BDD en libellés français
     * 
     * @param string $reasonKey Clé de raison en anglais
     * @return string Raison traduite en français
     */
    private function translateCongeReason(string $reasonKey): string {
        $translations = [
            'holiday' => 'Congé',
            'sickness' => 'Maladie',
            '' => 'Congé' // Valeur par défaut
        ];
        
        return $translations[$reasonKey] ?? $reasonKey;
    }

    /**
     * Génère une ligne de résumé pour le rapport
     * Formate les données de résumé en ligne de rapport
     *
     * @param string $firstName Prénom de l'utilisateur
     * @param string $lastName Nom de l'utilisateur
     * @param string $client Type de résumé
     * @param float $hours Nombre d'heures
     * @param string $customTimeValue Valeur personnalisée pour temps_passe (optionnel)
     * @return array
     */
    private function generateSummaryLine(string $firstName, string $lastName, string $client, float $hours, string $customTimeValue = ''): array {
        return $this->generateLine($firstName, $lastName, $client, '', $hours, $customTimeValue);
    }

    /**
     * Génère une ligne standard pour le rapport
     * Formate les données en ligne de rapport standardisée
     *
     * @param string $firstName Prénom de l'utilisateur
     * @param string $lastName Nom de l'utilisateur
     * @param string $client Nom du client ou type de ligne
     * @param string $project Nom du projet
     * @param float $hours Nombre d'heures
     * @param string $customTimeValue Valeur personnalisée pour temps_passe (optionnel)
     * @return array
     */
    private function generateLine(string $firstName, string $lastName, string $client, string $project, float $hours, string $customTimeValue = ''): array {
        return [
            'nom' => $lastName,
            'prenom' => $firstName,
            'client' => $client,
            'projet' => $project,
            'temps_passe' => $customTimeValue !== '' ? $customTimeValue : $hours . 'h',
        ];
    }

    /**
     * Trie le rapport hebdomadaire selon des critères spécifiques
     * Réorganise les lignes du rapport selon plusieurs critères de tri
     * 
     * @param array &$report Rapport à trier
     * @return void
     */
    private function sortWeeklyReport(array &$report): void {
        // Extraire les lignes de résumé global (Total Déclaré, Total Attendu, IT Room)
        $globalLines = array_filter($report, function ($line) {
            return $line['nom'] === 'Total' || $line['nom'] === 'IT Room';
        });

        // Extraire les lignes des utilisateurs
        $userLines = array_filter($report, function ($line) {
            return $line['nom'] !== 'Total' && $line['nom'] !== 'IT Room';
        });

        // Grouper les lignes par utilisateur
        $groupedUsers = [];
        foreach ($userLines as $line) {
            $userKey = $line['prenom'] . ' ' . $line['nom'];
            $groupedUsers[$userKey][] = $line;
        }

        // Trier les utilisateurs selon les critères demandés
        uasort($groupedUsers, function ($a, $b) {
            $totalDeclaredA = $this->extractHours($a, 'Temps déclaré (hors absence justifiée)');
            $totalOnSiteA = $this->extractHours($a, 'Temps sur site');
            $totalDeclaredB = $this->extractHours($b, 'Temps déclaré (hors absence justifiée)');
            $totalOnSiteB = $this->extractHours($b, 'Temps sur site');

            // Mettre les utilisateurs sans aucune déclaration en haut
            $hasNoDeclarationA = $this->hasNoDeclaration($a);
            $hasNoDeclarationB = $this->hasNoDeclaration($b);
            
            if ($hasNoDeclarationA && !$hasNoDeclarationB) {
                return -1;
            }
            if (!$hasNoDeclarationA && $hasNoDeclarationB) {
                return 1;
            }
            
            // Mettre les utilisateurs avec temps sur site = 0 (entièrement en congé) en bas
            if ($totalOnSiteA == 0 && $totalOnSiteB > 0) {
                return 1;
            }
            if ($totalOnSiteA > 0 && $totalOnSiteB == 0) {
                return -1;
            }

            // Ensuite, trier par ratio temps déclaré / temps sur site
            if ($totalDeclaredA < $totalOnSiteA && $totalDeclaredB >= $totalOnSiteB) {
                return -1;
            }
            if ($totalDeclaredA >= $totalOnSiteA && $totalDeclaredB < $totalOnSiteB) {
                return 1;
            }

            // Enfin, trier par nom
            return strcmp($a[0]['nom'], $b[0]['nom']);
        });

        // Reconstruire le rapport trié
        $sortedReport = [];
        
        // Ajouter d'abord les lignes de résumé global en tête
        foreach ($globalLines as $line) {
            $sortedReport[] = $line;
        }
        
        // Puis ajouter les lignes des utilisateurs
        foreach ($groupedUsers as $userLines) {
            foreach ($userLines as $line) {
                $sortedReport[] = $line;
            }
        }

        $report = $sortedReport;
    }

    /**
     * Vérifie si un utilisateur n'a aucune déclaration
     */
    private function hasNoDeclaration(array $userLines): bool
    {
        foreach ($userLines as $line) {
            if ($line['client'] === 'Aucune déclaration') {
                return true;
            }
        }
        return false;
    }

    /**
     * Extrait le nombre d'heures d'un type de ligne spécifique
     * Recherche une ligne par son type et extrait la valeur des heures
     * 
     * @param array $lines Lignes d'un utilisateur
     * @param string $client Type de ligne à extraire
     * @return float
     */
    private function extractHours(array $lines, string $client): float {
        foreach ($lines as $line) {
            if ($line['client'] === $client) {
                return (float)str_replace('h', '', $line['temps_passe']);
            }
        }
        return 0.0;
    }

    /**
     * Calcule le temps sur site en jours pour une période donnée
     * Détermine le nombre de jours ouvrés dans une période en excluant weekends et jours fériés
     * 
     * @param \DateTime $startDate Date de début
     * @param \DateTime $endDate Date de fin
     * @return float
     */
    private function calculateOnSiteTime(\DateTime $startDate, \DateTime $endDate): float {
        // S'assurer que les dates sont dans le bon ordre
        if ($startDate > $endDate) {
            $temp = $startDate;
            $startDate = $endDate;
            $endDate = $temp;
        }
        
        $holidays = $this->getHolidays();
        $totalOnSite = 0;
        $currentDate = clone $startDate;
        
        while ($currentDate <= $endDate) {
            $dayOfWeek = (int)$currentDate->format('N');
            $dateStr = $currentDate->format('Y-m-d');
            
            if ($dayOfWeek < 6 && !in_array($dateStr, $holidays)) {
                // Jour ouvré - ajouter 1 jour
                $totalOnSite += 1;
            }
            
            $currentDate->modify('+1 day');
        }
        
        return $totalOnSite;
    }
}
